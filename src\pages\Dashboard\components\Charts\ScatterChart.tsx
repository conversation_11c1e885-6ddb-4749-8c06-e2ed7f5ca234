'use client';

import type React from 'react';
import { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import type { ScatterChartProps } from './types';
import type { ThemeConfigValues } from '@/components/ChartSettings';
import ChartContainer from './ChartContainer';
import { getThemeColors } from '@/config/theme';

interface ExtendedScatterChartProps extends ScatterChartProps {
  themeConfig?: ThemeConfigValues;
}

const ScatterChart: React.FC<ExtendedScatterChartProps> = ({
  title = '散点图',
  showData = true,
  height = '100%',
  width = '100%',
  themeConfig,
  data = [
    { name: '点1', value: [10, 8.04] },
    { name: '点2', value: [8, 6.95] },
    { name: '点3', value: [13, 7.58] },
    { name: '点4', value: [9, 8.81] },
    { name: '点5', value: [11, 8.33] },
    { name: '点6', value: [14, 9.96] },
    { name: '点7', value: [6, 7.24] },
    { name: '点8', value: [4, 4.26] },
    { name: '点9', value: [12, 10.84] },
    { name: '点10', value: [7, 4.82] },
  ],
  symbolSize = 10,
}) => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    const chart = echarts.init(chartRef.current);

    const option = {
      color: getThemeColors(themeConfig?.theme),
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          let value = params.value;
          if (themeConfig?.showPrefixUnit) {
            value = `￥${value}`;
          }
          if (themeConfig?.showSuffixUnit) {
            value = `${value}%`;
          }
          return `${params.name}: (${value[0]}, ${value[1]})`;
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        top: '15%',
        containLabel: true,
        show: themeConfig?.showGrid,
        lineStyle: {
          type: themeConfig?.gridStyle === '实线' ? 'solid' : themeConfig?.gridStyle === '虚线' ? 'dashed' : 'dotted',
          width: Number(themeConfig?.gridWidth) || 1,
          color: themeConfig?.gridColor || '#f0f0f0',
        },
      },
      xAxis: {
        type: 'value',
        scale: true,
        axisLine: {
          lineStyle: {
            color: '#f0f0f0',
          },
        },
        axisLabel: {
          show: themeConfig?.showAxis,
          color: themeConfig?.textColor || 'rgba(0, 0, 0, 0.45)',
          fontSize: Number(themeConfig?.textSize) || 12,
          rotate: Number(themeConfig?.axisAngle?.replace('°', '')) || 0,
        },
        splitLine: {
          show: themeConfig?.showGrid,
          lineStyle: {
            type: themeConfig?.gridStyle === '实线' ? 'solid' : themeConfig?.gridStyle === '虚线' ? 'dashed' : 'dotted',
            width: Number(themeConfig?.gridWidth) || 1,
            color: themeConfig?.gridColor || '#f0f0f0',
          },
        },
      },
      yAxis: {
        type: 'value',
        scale: true,
        axisLine: {
          lineStyle: {
            color: '#f0f0f0',
          },
        },
        axisLabel: {
          show: themeConfig?.showAxis,
          color: themeConfig?.textColor || 'rgba(0, 0, 0, 0.45)',
          fontSize: Number(themeConfig?.textSize) || 12,
        },
        splitLine: {
          show: themeConfig?.showGrid,
          lineStyle: {
            type: themeConfig?.gridStyle === '实线' ? 'solid' : themeConfig?.gridStyle === '虚线' ? 'dashed' : 'dotted',
            width: Number(themeConfig?.gridWidth) || 1,
            color: themeConfig?.gridColor || '#f0f0f0',
          },
        },
      },
      legend: {
        show: themeConfig?.showLegend,
        data: ['数据点'],
        textStyle: {
          color: themeConfig?.textColor || 'rgba(0, 0, 0, 0.65)',
          fontSize: Number(themeConfig?.textSize) || 12,
        },
      },
      series: [
        {
          name: '数据点',
          type: 'scatter',
          data: data.map((item) => item.value),
          symbolSize: symbolSize,
          itemStyle: {
            color: themeConfig?.theme || '#2868E7',
          },
          label: {
            show: themeConfig?.showDataLabel,
            formatter: (params: any) => {
              const index = params.dataIndex;
              return data[index].name;
            },
            fontSize: Number(themeConfig?.textSize) || 12,
            color: themeConfig?.textColor || 'rgba(0, 0, 0, 0.65)',
          },
        },
      ],
      animation: themeConfig?.showAnimation,
    };

    chart.setOption(option);

    const handleResize = () => {
      chart.resize();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      chart.dispose();
      window.removeEventListener('resize', handleResize);
    };
  }, [data, symbolSize, themeConfig]);

  return <div ref={chartRef} style={{ width, height }} />;
};

// 包装ScatterChart，使其具备显示数据表格的能力
const ScatterChartWrapper: React.FC<ExtendedScatterChartProps> = (props) => {
  // 图表数据
  const chartData = [
    { name: '点1', value: [10, 8.04] },
    { name: '点2', value: [8, 6.95] },
    { name: '点3', value: [13, 7.58] },
    { name: '点4', value: [9, 8.81] },
    { name: '点5', value: [11, 8.33] },
    { name: '点6', value: [14, 9.96] },
    { name: '点7', value: [6, 7.24] },
    { name: '点8', value: [4, 4.26] },
    { name: '点9', value: [12, 10.84] },
    { name: '点10', value: [7, 4.82] },
  ];

  // 表格显示用的列定义
  const tableColumns = [
    { title: '名称', dataIndex: 'name', key: 'name' },
    { title: 'X值', dataIndex: ['value', 0], key: 'x' },
    { title: 'Y值', dataIndex: ['value', 1], key: 'y' },
  ];

  return (
    <ChartContainer title="散点图" data={chartData} columns={tableColumns}>
      <ScatterChart {...props} />
    </ChartContainer>
  );
};

export default ScatterChartWrapper;
