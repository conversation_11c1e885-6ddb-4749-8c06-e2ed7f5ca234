'use client';

import type React from 'react';
import { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import type { ComboChartProps } from './types';
import type { ThemeConfigValues } from '@/components/ChartSettings';
import ChartContainer from './ChartContainer';
import { getThemeColors } from '@/config/theme';

interface ExtendedComboChartProps extends ComboChartProps {
  themeConfig?: ThemeConfigValues;
  withContainer?: boolean; // 新增属性：是否显示 ChartContainer，默认 true
}

const ComboChart: React.FC<ExtendedComboChartProps> = ({
  title = '组合图',
  showData = true,
  height = '100%',
  width = '100%',
  themeConfig,
  barData = [
    { name: '1月', value: 2.0 },
    { name: '2月', value: 4.9 },
    { name: '3月', value: 7.0 },
    { name: '4月', value: 23.2 },
    { name: '5月', value: 25.6 },
    { name: '6月', value: 76.7 },
    { name: '7月', value: 135.6 },
    { name: '8月', value: 162.2 },
    { name: '9月', value: 32.6 },
    { name: '10月', value: 20.0 },
    { name: '11月', value: 6.4 },
    { name: '12月', value: 3.3 },
  ],
  lineData = [
    { name: '1月', value: 2.0 },
    { name: '2月', value: 2.2 },
    { name: '3月', value: 3.3 },
    { name: '4月', value: 4.5 },
    { name: '5月', value: 6.3 },
    { name: '6月', value: 10.2 },
    { name: '7月', value: 20.3 },
    { name: '8月', value: 23.4 },
    { name: '9月', value: 23.0 },
    { name: '10月', value: 16.5 },
    { name: '11月', value: 12.0 },
    { name: '12月', value: 6.2 },
  ],
}) => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    const chart = echarts.init(chartRef.current);

    const option = {
      color: getThemeColors(themeConfig?.theme),
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999',
          },
        },
        formatter: (params: any) => {
          let result = `${params[0].name}<br/>`;
          params.forEach((param: any) => {
            let value = param.value;
            if (themeConfig?.showPrefixUnit) {
              value = `￥${value}`;
            }
            if (themeConfig?.showSuffixUnit) {
              value = `${value}%`;
            }
            result += `${param.seriesName}: ${value}<br/>`;
          });
          return result;
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        top: '15%',
        containLabel: true,
        show: themeConfig?.showGrid,
        lineStyle: {
          type: themeConfig?.gridStyle === '实线' ? 'solid' : themeConfig?.gridStyle === '虚线' ? 'dashed' : 'dotted',
          width: Number(themeConfig?.gridWidth) || 1,
          color: themeConfig?.gridColor || '#f0f0f0',
        },
      },
      xAxis: {
        type: 'category',
        data: barData.map((item) => item.name),
        axisLine: {
          lineStyle: {
            color: '#f0f0f0',
          },
        },
        axisLabel: {
          show: themeConfig?.showAxis,
          color: themeConfig?.textColor || 'rgba(0, 0, 0, 0.45)',
          fontSize: Number(themeConfig?.textSize) || 12,
          rotate: Number(themeConfig?.axisAngle?.replace('°', '')) || 0,
        },
      },
      yAxis: [
        {
          type: 'value',
          name: '柱状图',
          max: themeConfig?.maxValue === '自动' ? undefined : Number(themeConfig?.maxValue),
          min: themeConfig?.minValue === '自动' ? undefined : Number(themeConfig?.minValue),
          interval: themeConfig?.dataInterval === '自动' ? undefined : Number(themeConfig?.dataInterval),
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: themeConfig?.showAxis,
            color: themeConfig?.textColor || 'rgba(0, 0, 0, 0.45)',
            fontSize: Number(themeConfig?.textSize) || 12,
          },
          splitLine: {
            show: themeConfig?.showGrid,
            lineStyle: {
              type: themeConfig?.gridStyle === '实线' ? 'solid' : themeConfig?.gridStyle === '虚线' ? 'dashed' : 'dotted',
              width: Number(themeConfig?.gridWidth) || 1,
              color: themeConfig?.gridColor || '#f0f0f0',
            },
          },
        },
        {
          type: 'value',
          name: '折线图',
          max: themeConfig?.maxValue === '自动' ? undefined : Number(themeConfig?.maxValue),
          min: themeConfig?.minValue === '自动' ? undefined : Number(themeConfig?.minValue),
          interval: themeConfig?.dataInterval === '自动' ? undefined : Number(themeConfig?.dataInterval),
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: themeConfig?.showAxis,
            color: themeConfig?.textColor || 'rgba(0, 0, 0, 0.45)',
            fontSize: Number(themeConfig?.textSize) || 12,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      legend: {
        show: themeConfig?.showLegend,
        data: ['预算分配', '实际开销'],
        textStyle: {
          color: themeConfig?.textColor || 'rgba(0, 0, 0, 0.65)',
          fontSize: Number(themeConfig?.textSize) || 12,
        },
      },
      series: [
        {
          name: '预算分配',
          type: 'bar',
          barWidth: `${themeConfig?.widthRatio || 30}%`,
          data: barData.map((item) => item.value),
          // itemStyle: {
          //   color: themeConfig?.theme || '#2868E7',
          // },
          label: {
            show: themeConfig?.showDataLabel,
            formatter: (params: any) => {
              let value = params.value;
              if (themeConfig?.showPrefixUnit) {
                value = `￥${value}`;
              }
              if (themeConfig?.showSuffixUnit) {
                value = `${value}%`;
              }
              return value;
            },
            fontSize: Number(themeConfig?.textSize) || 12,
            color: themeConfig?.textColor || 'rgba(0, 0, 0, 0.65)',
          },
        },
        {
          name: '实际开销',
          type: 'line',
          yAxisIndex: 1,
          data: lineData.map((item) => item.value),
          // itemStyle: {
          //   color: '#52c41a',
          // },
          symbolSize: 8,
          smooth: true,
          label: {
            show: themeConfig?.showDataLabel,
            formatter: (params: any) => {
              let value = params.value;
              if (themeConfig?.showPrefixUnit) {
                value = `￥${value}`;
              }
              if (themeConfig?.showSuffixUnit) {
                value = `${value}%`;
              }
              return value;
            },
            fontSize: Number(themeConfig?.textSize) || 12,
            color: themeConfig?.textColor || 'rgba(0, 0, 0, 0.65)',
          },
        },
      ],
      animation: themeConfig?.showAnimation,
    };

    chart.setOption(option);

    const handleResize = () => {
      chart.resize();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      chart.dispose();
      window.removeEventListener('resize', handleResize);
    };
  }, [barData, lineData, themeConfig]);

  return <div ref={chartRef} style={{ width, height }} />;
};

const ComboChartWrapper: React.FC<ExtendedComboChartProps> = ({
  withContainer = true, // 设置默认值为 true
  ...props
}) => {
  const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月'];
  const chartData = months.map((month, index) => ({
    month,
    budget: props.barData?.[index]?.value ?? 0,
    actual: props.lineData?.[index]?.value ?? 0,
  }));

  const tableColumns = [
    { title: '月份', dataIndex: 'month', key: 'month' },
    { title: '预算分配', dataIndex: 'budget', key: 'budget' },
    { title: '实际开销', dataIndex: 'actual', key: 'actual' },
  ];

  if (withContainer) {
    return (
      <ChartContainer title="组合图" data={chartData} columns={tableColumns}>
        <ComboChart {...props} />
      </ChartContainer>
    );
  }

  return <ComboChart {...props} />;
};

export default ComboChartWrapper;
