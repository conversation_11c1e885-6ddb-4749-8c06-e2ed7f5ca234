'use client';

import type React from 'react';
import { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import type { BarChartProps } from './types';
import type { ThemeConfigValues } from '@/components/ChartSettings';
import ChartContainer from './ChartContainer';
import { getThemeColors } from '@/config/theme';

interface ExtendedHorizontalBarChartProps extends BarChartProps {
  themeConfig?: ThemeConfigValues;
}

const HorizontalBarChart: React.FC<ExtendedHorizontalBarChartProps> = ({
  title = '条形图',
  showData = true,
  height = '100%',
  width = '100%',
  themeConfig,
  data = [
    { name: '类别E', value: 8 },
    { name: '类别D', value: 10 },
    { name: '类别C', value: 12 },
    { name: '类别B', value: 14 },
    { name: '类别A', value: 16 },
  ],
}) => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    const chart = echarts.init(chartRef.current);

    const option = {
      color: getThemeColors(themeConfig?.theme),
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: (params: any) => {
          let value = params[0].value;
          if (themeConfig?.showPrefixUnit) {
            value = `￥${value}`;
          }
          if (themeConfig?.showSuffixUnit) {
            value = `${value}%`;
          }
          return `${params[0].name}: ${value}`;
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '3%',
        containLabel: true,
        show: themeConfig?.showGrid,
        lineStyle: {
          type: themeConfig?.gridStyle === '实线' ? 'solid' : themeConfig?.gridStyle === '虚线' ? 'dashed' : 'dotted',
          width: Number(themeConfig?.gridWidth) || 1,
          color: themeConfig?.gridColor || '#f0f0f0',
        },
      },
      xAxis: {
        type: 'value',
        max: themeConfig?.maxValue === '自动' ? undefined : Number(themeConfig?.maxValue),
        min: themeConfig?.minValue === '自动' ? undefined : Number(themeConfig?.minValue),
        interval: themeConfig?.dataInterval === '自动' ? undefined : Number(themeConfig?.dataInterval),
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: themeConfig?.showAxis,
          color: themeConfig?.textColor || 'rgba(0, 0, 0, 0.45)',
          fontSize: Number(themeConfig?.textSize) || 12,
        },
        splitLine: {
          show: themeConfig?.showGrid,
          lineStyle: {
            type: themeConfig?.gridStyle === '实线' ? 'solid' : themeConfig?.gridStyle === '虚线' ? 'dashed' : 'dotted',
            width: Number(themeConfig?.gridWidth) || 1,
            color: themeConfig?.gridColor || '#f0f0f0',
          },
        },
      },
      yAxis: {
        type: 'category',
        data: data.map((item) => item.name),
        axisLine: {
          lineStyle: {
            color: '#f0f0f0',
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: themeConfig?.showAxis,
          color: themeConfig?.textColor || 'rgba(0, 0, 0, 0.45)',
          fontSize: Number(themeConfig?.textSize) || 12,
        },
      },
      legend: {
        show: themeConfig?.showLegend,
        data: ['数值'],
        textStyle: {
          color: themeConfig?.textColor || 'rgba(0, 0, 0, 0.65)',
          fontSize: Number(themeConfig?.textSize) || 12,
        },
      },
      series: [
        {
          name: '数值',
          type: 'bar',
          barWidth: `${themeConfig?.widthRatio || 60}%`,
          data: data.map((item) => item.value),
          itemStyle: {
            color: themeConfig?.theme || '#2868E7',
            borderRadius: [0, 4, 4, 0],
          },
          label: {
            show: themeConfig?.showDataLabel,
            position: 'right',
            formatter: (params: any) => {
              let value = params.value;
              if (themeConfig?.showPrefixUnit) {
                value = `￥${value}`;
              }
              if (themeConfig?.showSuffixUnit) {
                value = `${value}%`;
              }
              return value;
            },
            fontSize: Number(themeConfig?.textSize) || 12,
            color: themeConfig?.textColor || 'rgba(0, 0, 0, 0.65)',
          },
        },
      ],
      animation: themeConfig?.showAnimation,
    };

    chart.setOption(option);

    const handleResize = () => {
      chart.resize();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      chart.dispose();
      window.removeEventListener('resize', handleResize);
    };
  }, [data, themeConfig]);

  return <div ref={chartRef} style={{ width, height }} />;
};

// 包装HorizontalBarChart，使其具备显示数据表格的能力
const HorizontalBarChartWrapper: React.FC<ExtendedHorizontalBarChartProps> = (props) => {
  // 图表数据
  const chartData = [
    { name: '类别E', value: 8 },
    { name: '类别D', value: 10 },
    { name: '类别C', value: 12 },
    { name: '类别B', value: 14 },
    { name: '类别A', value: 16 },
  ];

  // 表格显示用的列定义
  const tableColumns = [
    { title: '类别', dataIndex: 'name', key: 'name' },
    { title: '数值', dataIndex: 'value', key: 'value' },
  ];

  return (
    <ChartContainer title="条形图" data={chartData} columns={tableColumns}>
      <HorizontalBarChart {...props} />
    </ChartContainer>
  );
};

export default HorizontalBarChartWrapper;
