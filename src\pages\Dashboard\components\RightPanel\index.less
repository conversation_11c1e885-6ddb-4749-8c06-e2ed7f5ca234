.right-panel {
  width: 100%;
  height: 100%;
  .ant-tabs-nav {
    margin-bottom: 0;
  }

  .collapse-icon {
    width: 20px;
    height: 20px;
    cursor: pointer;
  }

  .analysis-content {
    height: calc(100vh - 200px);
    padding-top: 16px;
    overflow-y: auto;
  }
  .paragraph-body {
    h3 {
      width: max-content;
      padding: 2px 8px;
      font-weight: 600;
      border-radius: 4px;
      font-size: 14px;
    }
    h3:nth-of-type(1) {
      border: 1px solid #50b3f5;
      background: rgba(142, 207, 251, 0.6);
    }
    h3:nth-of-type(2) {
      border: 1px solid #e86541;
      background: rgba(239, 151, 151, 0.6);
    }
    h3:nth-of-type(3) {
      border: 1px solid #1d4fd7;
      background: rgba(29, 79, 215, 0.6);
    }
    h3:nth-of-type(4) {
      border: 1px solid #e86541;
      background: rgba(239, 151, 151, 0.6);
    }
    h3:nth-of-type(5) {
      border: 1px solid #e86541;
      background: rgba(239, 151, 151, 0.6);
    }
  }
}
