import React, { useState, useEffect } from 'react';
import { Tabs, Typography } from 'antd';
import ReactMarkdown from 'react-markdown'; // React 渲染 Markdown 的核心库
import remarkGfm from 'remark-gfm'; // 支持 GitHub 风格的 Markdown（如表格、任务列表等）
import remarkBreaks from 'remark-breaks'; // 把换行符\n渲染成 <br>
import rehypeRaw from 'rehype-raw'; // 允许 Markdown 里混用 HTML
import * as echarts from 'echarts/core';
import type { EChartsCoreOption } from 'echarts/core';
import type { SeriesOption } from 'echarts/types/dist/shared';
import ChartSettings, { ThemeConfigValues } from '@/components/ChartSettings';
import { getThemeColors } from '@/config/theme';
import { CHART_TYPES, CHART_STYLE } from '@/config/charts';
import './index.less';
const { Title, Paragraph, Text } = Typography;

interface RightPanelProps {
  onSettingsChange?: (settings: ThemeConfigValues) => void;
  showAnalysis?: boolean;
  chartType?: string;
  onAnalyze?: boolean;
  chartStyle?: ThemeConfigValues;
  analysisRes?: string;
  onCollapse?: (collapsed: boolean) => void;
}

const RightPanel: React.FC<RightPanelProps> = ({ onSettingsChange, chartType, onAnalyze = false, chartStyle, analysisRes, onCollapse }) => {
  // 状态管理
  const [settings, setSettings] = useState<ThemeConfigValues>(() => {
    return chartStyle ? chartStyle : CHART_STYLE;
  });
  const [activeTab, setActiveTab] = useState('settings');

  // 当 onAnalyze 为 true 时，自动切换到分析标签页
  useEffect(() => {
    if (onAnalyze && analysisRes) {
      setActiveTab('analysis');
    } else {
      setActiveTab('settings');
    }
  }, [onAnalyze, analysisRes]);

  // 当 chartStyle 变化时更新 settings
  useEffect(() => {
    if (chartStyle && typeof chartStyle === 'string') {
      setSettings(JSON.parse(chartStyle));
    } else {
      setSettings(chartStyle || CHART_STYLE);
    }
  }, [chartStyle]);

  // 处理设置变更
  const handleSettingChange = (config: ThemeConfigValues) => {
    setSettings(config);
    onSettingsChange?.(config);
  };

  const handleCollapse = () => {
    onCollapse?.(true);
  };

  const items = [
    ...(analysisRes && onAnalyze
      ? [
          {
            key: 'analysis',
            label: '智能分析结果',
            children: (
              <Paragraph className="analysis-content paragraph-body">
                <ReactMarkdown remarkPlugins={[remarkGfm, remarkBreaks]} rehypePlugins={[rehypeRaw]}>
                  {analysisRes}
                </ReactMarkdown>
              </Paragraph>
            ),
          },
        ]
      : []),
    {
      key: 'settings',
      label: '参数设置',
      children: <ChartSettings onChange={handleSettingChange} defaultValues={settings} chartType={chartType as any} layout="vertical" />,
    },
  ];

  return (
    <div className="right-panel">
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={items}
        tabBarExtraContent={{
          right: <img className="collapse-icon" src="/assets/r2l6w3pe.svg" alt="收缩面板" onClick={handleCollapse} />,
        }}
      />
    </div>
  );
};

export default RightPanel;
