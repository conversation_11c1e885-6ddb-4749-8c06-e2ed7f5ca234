'use client';

import type React from 'react';
import { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import 'echarts-wordcloud';
import type { WordCloudProps } from './types';
import ChartContainer from './ChartContainer';

const WordCloudChart: React.FC<WordCloudProps> = ({
  title = '词云图',
  showData = true,
  height = '100%',
  width = '100%',
  data = [
    { name: '产品', value: 38 },
    { name: '设计', value: 32 },
    { name: '开发', value: 30 },
    { name: '测试', value: 28 },
    { name: '项目管理', value: 26 },
    { name: '用户体验', value: 24 },
    { name: '数据分析', value: 22 },
    { name: '服务器', value: 20 },
    { name: '客户端', value: 18 },
    { name: '性能优化', value: 16 },
    { name: '架构设计', value: 14 },
    { name: '代码审查', value: 12 },
    { name: '运维', value: 10 },
    { name: '安全', value: 8 },
    { name: '前端', value: 6 },
    { name: '后端', value: 4 },
    { name: '算法', value: 3 },
  ],
  sizeRange = [16, 50],
  shape = 'circle',
}) => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    const chart = echarts.init(chartRef.current);

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: function (params: any) {
          return `${params.name}: ${params.value}`;
        },
      },
      series: [
        {
          type: 'wordCloud',
          shape: shape,
          left: 'center',
          top: 'center',
          width: '100%',
          height: '90%',
          right: null,
          bottom: null,
          sizeRange: sizeRange,
          rotationRange: [-45, 45],
          rotationStep: 15,
          gridSize: 8,
          drawOutOfBound: false,
          textStyle: {
            fontFamily: 'sans-serif',
            fontWeight: 'bold',
            color: function () {
              return (
                'rgb(' +
                [Math.round(Math.random() * 160 + 50), Math.round(Math.random() * 160 + 50), Math.round(Math.random() * 160 + 50)].join(
                  ',',
                ) +
                ')'
              );
            },
          },
          emphasis: {
            textStyle: {
              shadowBlur: 10,
              shadowColor: '#333',
            },
          },
          data: data,
        },
      ],
    };

    chart.setOption(option);

    const handleResize = () => {
      chart.resize();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      chart.dispose();
      window.removeEventListener('resize', handleResize);
    };
  }, [data, sizeRange, shape]);

  return <div ref={chartRef} style={{ width, height }} />;
};

// 包装WordCloudChart，使其具备显示数据表格的能力
const WordCloudChartWrapper: React.FC<WordCloudProps> = (props) => {
  // 图表数据
  const chartData = [
    { name: '产品', value: 38 },
    { name: '设计', value: 32 },
    { name: '开发', value: 30 },
    { name: '测试', value: 28 },
    { name: '项目管理', value: 26 },
    { name: '用户体验', value: 24 },
    { name: '数据分析', value: 22 },
    { name: '服务器', value: 20 },
    { name: '客户端', value: 18 },
    { name: '性能优化', value: 16 },
    { name: '架构设计', value: 14 },
    { name: '代码审查', value: 12 },
    { name: '运维', value: 10 },
    { name: '安全', value: 8 },
    { name: '前端', value: 6 },
    { name: '后端', value: 4 },
    { name: '算法', value: 3 },
  ];

  // 表格显示用的列定义
  const tableColumns = [
    { title: '关键词', dataIndex: 'name', key: 'name' },
    { title: '权重', dataIndex: 'value', key: 'value' },
  ];

  return (
    <ChartContainer title="词云" data={chartData} columns={tableColumns}>
      <WordCloudChart {...props} />
    </ChartContainer>
  );
};

export default WordCloudChartWrapper;
