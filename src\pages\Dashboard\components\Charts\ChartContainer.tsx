import type React from 'react';
import { SettingOutlined } from '@ant-design/icons';
import { useState, useEffect } from 'react';
import { Table, Dropdown, Menu, Modal, Input, message, Tooltip } from 'antd';
import FullScreenChartModal from '../FullScreenChartModal/index';
import { editChart, deleteChart } from '@/services/DataLoom/yibiaopanjiekou';
import { extractTableFromOption } from '@/utils/echartsToTable';

interface ChartContainerProps {
  title: string;
  children: React.ReactNode;
  showFullScreenLink?: boolean;
  showAnalyze?: boolean; // 添加控制智能分析按钮显示的属性
  data?: any[]; // 表格数据
  columns?: any[]; // 表格列定义
  chartId: string; // 添加chartId属性
  dashboardId: string; // 添加dashboardId属性
  chartName: string; // 添加chartName属性
  onRename?: (newTitle: string) => void;
  onDelete?: () => void;
  onAnalyze?: () => void; // 添加智能分析回调
  chartOption?: any; // 添加图表配置选项
}

const ChartContainer: React.FC<ChartContainerProps> = ({
  title,
  children,
  showFullScreenLink = true,
  showAnalyze = true, // 设置默认值为 true
  data = [],
  columns = [],
  chartId,
  dashboardId,
  chartName,
  onRename,
  onDelete,
  onAnalyze,
  chartOption,
}) => {
  const [showChart, setShowChart] = useState(true);
  const [isFullScreenVisible, setIsFullScreenVisible] = useState(false);
  const [isRenameModalVisible, setIsRenameModalVisible] = useState(false);
  const [newTitle, setNewTitle] = useState(title);
  const [tableData, setTableData] = useState<{ columns: any[]; dataSource: any[] }>({ columns: [], dataSource: [] });

  useEffect(() => {
    if (chartOption) {
      const { columns: extractedColumns, dataSource } = extractTableFromOption(chartOption);
      setTableData({
        columns: extractedColumns,
        dataSource: dataSource,
      });
    }
  }, [chartOption]);

  const showFullScreen = () => {
    setIsFullScreenVisible(true);
  };

  const hideFullScreen = () => {
    setIsFullScreenVisible(false);
  };

  const toggleChart = () => {
    setShowChart(!showChart);
  };

  const handleRename = async () => {
    try {
      const params = {
        dashboardId: dashboardId,
        chartName: newTitle,
        id: chartId,
      };
      const res = await editChart(params);
      if (res.code === 0) {
        if (onRename) {
          onRename(newTitle);
        }
        message.success('重命名成功');
        setIsRenameModalVisible(false);
      } else {
        message.error(res.message || '重命名失败');
      }
    } catch (error) {
      message.error('重命名失败');
    }
  };

  const handleDelete = () => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个图表吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const params = {
            chartId: chartId,
          };
          await deleteChart(params);
          if (onDelete) {
            onDelete();
          }
          message.success('删除成功');
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };

  return (
    <div className="chart-container">
      <div className="chart-header">
        <Tooltip title={title}>
          <span className="chart-title">{title}</span>
        </Tooltip>
        <div className="chart-actions">
          {showAnalyze && (
            <span className="action-link" onClick={onAnalyze}>
              智能分析
            </span>
          )}
          <span className="action-link" onClick={toggleChart}>
            {showChart ? '显示数据' : '显示图表'}
          </span>
          {showFullScreenLink && (
            <span className="action-link" onClick={showFullScreen}>
              查看大图
            </span>
          )}
          <Dropdown
            menu={{
              items: [
                {
                  key: 'rename',
                  label: '重命名',
                  onClick: () => setIsRenameModalVisible(true),
                },
                {
                  key: 'delete',
                  label: '删除',
                  onClick: handleDelete,
                },
              ],
            }}
            trigger={['click']}
          >
            <SettingOutlined className="setting-icon" />
          </Dropdown>
        </div>
      </div>
      <div className="chart-content">
        {showChart ? (
          children
        ) : (
          <Table
            dataSource={tableData.dataSource}
            columns={tableData.columns}
            pagination={false}
            size="small"
            scroll={{ y: 200 }}
            bordered
          />
        )}
      </div>

      <FullScreenChartModal
        visible={isFullScreenVisible}
        onClose={hideFullScreen}
        title={title}
        width="80%"
        height="85vh"
        showSettings={true}
        initialMode={showChart ? 'chart' : 'table'}
        chartOption={chartOption}
        onRename={() => setIsRenameModalVisible(true)}
        onDelete={() => handleDelete()}
      >
        {children}
      </FullScreenChartModal>

      <Modal
        title="重命名图表"
        className="rename-chart-modal common-modal"
        zIndex={9999} //最大层级
        open={isRenameModalVisible}
        onOk={handleRename}
        onCancel={() => setIsRenameModalVisible(false)}
      >
        <Input value={newTitle} onChange={(e) => setNewTitle(e.target.value)} placeholder="请输入新的图表名称" />
      </Modal>
    </div>
  );
};

export default ChartContainer;
