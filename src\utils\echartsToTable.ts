interface EChartsDatasetOption {
  source: Record<string, any>[]; // 每一行为一个数据项
  dimensions?: string[]; // 可选，用于明确列顺序
}

interface EChartsOptionWithDataset {
  dataset?: EChartsDatasetOption;
}

interface TableColumn {
  title: string;
  dataIndex: string;
  key: string;
}

interface TableDataSourceItem {
  key: string;
  [key: string]: any;
}

interface TableResult {
  columns: TableColumn[];
  dataSource: TableDataSourceItem[];
}

/**
 * 从使用 dataset 的 ECharts 配置中提取表格数据
 * @param option ECharts 配置项（dataset 格式）
 * @param columnMap 自定义列标题映射（可选）
 */
export function extractTableFromOption(option: EChartsOptionWithDataset, columnMap?: Record<string, string>): TableResult {
  const source = option.dataset?.source;

  if (!Array.isArray(source) || source.length === 0) {
    return {
      columns: [],
      dataSource: [],
    };
  }

  const firstRow = source[0];
  const keys = Object.keys(firstRow);

  const columns: TableColumn[] = keys.map((key) => ({
    title: columnMap?.[key] || key,
    dataIndex: key,
    key,
  }));

  const dataSource: TableDataSourceItem[] = source.map((row, index) => ({
    key: String(index + 1),
    ...row,
  }));

  return {
    columns,
    dataSource,
  };
}
