import type { ThemeConfigValues } from '@/components/ChartSettings';

export interface ChartProps {
  title?: string;
  showData?: boolean;
  height?: string | number;
  width?: string | number;
  themeConfig?: ThemeConfigValues;
}

export interface BarChartProps extends ChartProps {
  data?: {
    name: string;
    value: number;
  }[];
  color?: string | string[];
}

export interface LineChartProps extends ChartProps {
  data?: {
    name: string;
    value: number;
    date: string;
  }[];
  smooth?: boolean;
  areaStyle?: boolean | object;
}

export interface PieChartProps extends ChartProps {
  data?: {
    name: string;
    value: number;
  }[];
  radius?: string | number | (string | number)[];
  donut?: boolean;
}

export interface ScatterChartProps extends ChartProps {
  data?: {
    name: string;
    value: number[];
  }[];
  symbolSize?: number | Function;
}

export interface ComboChartProps extends ChartProps {
  barData?: {
    name: string;
    value: number;
  }[];
  lineData?: {
    name: string;
    value: number;
  }[];
}

export interface FunnelChartProps extends ChartProps {
  data?: {
    name: string;
    value: number;
  }[];
  sort?: 'ascending' | 'descending' | 'none';
}

export interface WordCloudProps extends ChartProps {
  data?: {
    name: string;
    value: number;
  }[];
  sizeRange?: number[];
  shape?: string;
}
