'use client';

import type React from 'react';
import { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import type { BarChartProps } from './types';
import type { ThemeConfigValues } from '@/components/ChartSettings';
import ChartContainer from './ChartContainer';
import { getThemeColors } from '@/config/theme';

interface ExtendedBarChartProps extends BarChartProps {
  themeConfig?: ThemeConfigValues;
}

const BarChart: React.FC<ExtendedBarChartProps> = ({ title, showData = true, themeConfig }) => {
  const chartRef = useRef<HTMLDivElement>(null);

  // 图表数据
  const chartData = [
    { category: '类型', value: 2.5 },
    { category: '非常重要', value: 1.8 },
    { category: '重要', value: 2.2 },
    { category: '一般', value: 3.0 },
    { category: '较重要', value: 2.0 },
    { category: '紧急', value: 2.5 },
  ];

  // 表格显示用的列定义
  const tableColumns = [
    { title: '类别', dataIndex: 'category', key: 'category' },
    { title: '数值', dataIndex: 'value', key: 'value' },
  ];

  useEffect(() => {
    if (!chartRef.current) return;

    const chart = echarts.init(chartRef.current);

    const option = {
      color: getThemeColors(themeConfig?.theme),
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        top: '15%',
        containLabel: true,
        show: themeConfig?.showGrid,
        lineStyle: {
          type: themeConfig?.gridStyle === '实线' ? 'solid' : themeConfig?.gridStyle === '虚线' ? 'dashed' : 'dotted',
          width: Number(themeConfig?.gridWidth) || 1,
          color: themeConfig?.gridColor || '#f0f0f0',
        },
      },
      xAxis: {
        type: 'category',
        data: chartData.map((item) => item.category),
        axisLine: {
          lineStyle: {
            color: '#f0f0f0',
          },
        },
        axisLabel: {
          show: themeConfig?.showAxis,
          color: themeConfig?.textColor || 'rgba(0, 0, 0, 0.45)',
          fontSize: Number(themeConfig?.textSize) || 12,
          rotate: Number(themeConfig?.axisAngle?.replace('°', '')) || 0,
        },
      },
      yAxis: {
        type: 'value',
        max: themeConfig?.maxValue === '自动' ? undefined : Number(themeConfig?.maxValue),
        min: themeConfig?.minValue === '自动' ? undefined : Number(themeConfig?.minValue),
        interval: themeConfig?.dataInterval === '自动' ? undefined : Number(themeConfig?.dataInterval),
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: themeConfig?.showAxis,
          color: themeConfig?.textColor || 'rgba(0, 0, 0, 0.45)',
          fontSize: Number(themeConfig?.textSize) || 12,
        },
        splitLine: {
          show: themeConfig?.showGrid,
          lineStyle: {
            type: themeConfig?.gridStyle === '实线' ? 'solid' : themeConfig?.gridStyle === '虚线' ? 'dashed' : 'dotted',
            width: Number(themeConfig?.gridWidth) || 1,
            color: themeConfig?.gridColor || '#f0f0f0',
          },
        },
      },
      legend: {
        show: themeConfig?.showLegend,
      },
      series: [
        {
          name: '产品系列',
          type: 'bar',
          barWidth: `${themeConfig?.widthRatio || 30}%`,
          data: chartData.map((item) => item.value),
          label: {
            show: themeConfig?.showDataLabel,
            formatter: (params: any) => {
              let value = params.value;
              if (themeConfig?.showPrefixUnit) {
                value = `￥${value}`;
              }
              if (themeConfig?.showSuffixUnit) {
                value = `${value}%`;
              }
              return value;
            },
            fontSize: Number(themeConfig?.textSize) || 12,
            color: themeConfig?.textColor || 'rgba(0, 0, 0, 0.65)',
          },
        },
      ],
      animation: themeConfig?.showAnimation,
    };

    chart.setOption(option);

    const handleResize = () => {
      chart.resize();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      chart.dispose();
      window.removeEventListener('resize', handleResize);
    };
  }, [themeConfig]);

  return <div ref={chartRef} style={{ width: '100%', height: '100%' }} />;
};

// 包装BarChart，使其具备显示数据表格的能力
const BarChartWrapper: React.FC<ExtendedBarChartProps> = (props) => {
  // 图表数据
  const chartData = [
    { category: '类型', value: 2.5 },
    { category: '非常重要', value: 1.8 },
    { category: '重要', value: 2.2 },
    { category: '一般', value: 3.0 },
    { category: '较重要', value: 2.0 },
    { category: '紧急', value: 2.5 },
  ];

  // 表格显示用的列定义
  const tableColumns = [
    { title: '类别', dataIndex: 'category', key: 'category' },
    { title: '数值', dataIndex: 'value', key: 'value' },
  ];

  return (
    <ChartContainer title="柱状图" data={chartData} columns={tableColumns}>
      <BarChart {...props} />
    </ChartContainer>
  );
};

export default BarChartWrapper;
