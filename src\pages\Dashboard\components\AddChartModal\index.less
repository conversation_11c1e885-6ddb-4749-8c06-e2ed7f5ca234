.chart-selector-modal {
  .ant-modal-content {
    border-radius: 10px;
    background: #fff;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  }
  .add-chart-modal-layout {
    display: flex;
    flex-direction: row;
    height: 700px;
    min-width: 1200px;
    border-radius: 8px;
    overflow: hidden;
  }
  .add-chart-sidebar {
    width: 240px;
    background: #fff;
    border-right: 1px dashed #9ca3af;
    display: flex;
    flex-direction: column;
    gap: 16px;
    .ant-btn-link {
      color: #2868e7;
      font-size: 13px;
      margin-top: 4px;
    }
    .field-list {
      width: calc(100% - 24px);
      display: flex;
      flex-direction: column;
      gap: 6px;
      flex: 1;
      min-height: 0; /* 关键：允许 flex 子项收缩 */
      padding-right: 4px;
      overflow-y: auto;
    }
    .field-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: #f6f8fa;
      border-radius: 4px;
      padding: 6px 10px;
      font-size: 14px;
      color: #333;
      cursor: grab;
      transition:
        box-shadow 0.2s,
        background 0.2s;
      border: 1px solid transparent;
      .field-left {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;
        overflow: hidden;
      }
      .field-icon {
        color: #2868e7;
        margin-right: 6px;
        font-weight: bold;
        flex-shrink: 0;
      }

      // 为字段项中的字段名称添加防溢出样式
      .field-name {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        min-width: 0;
      }
      .used-indicator {
        font-size: 12px;
        color: #999;
        background: #e6e6e6;
        padding: 2px 6px;
        border-radius: 10px;
        margin-left: auto;
      }
      &.used {
        background: #f0f0f0;
        color: #999;
        cursor: not-allowed;
        .field-icon {
          color: #999;
        }
        &:hover {
          border: 1px solid #d9d9d9;
          background: #f0f0f0;
        }
      }
      &.dragging {
        opacity: 0.5;
        background: #9ca3af;
      }
      &:hover:not(.used) {
        border: 1px solid #2868e7;
        background: #eaf2ff;
      }
    }
  }
  .add-chart-center {
    max-width: 300px;
    min-width: 300px;
    flex: 1 1 0;
    padding: 0 16px;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 0;
    .ant-tabs {
      .ant-tabs-nav {
        margin-bottom: 18px;
      }
      .ant-tabs-tab {
        font-size: 15px;
        padding: 6px 24px;
      }
    }
    .config-section {
      display: flex;
      flex-direction: column;
      flex: 1;
      min-height: 0;
      padding-right: 8px;
      overflow-y: auto;

      .config-title {
        font-size: 14px;
        color: #262628;
        margin-bottom: 10px;
        &:first-child {
          margin-top: 0;
        }
      }
      > .ant-select {
        width: 100%;
        margin-bottom: 18px;
      }
      .drop-area {
        min-height: 44px;
        max-height: 120px;
        overflow-x: hidden;
        overflow-y: auto;
        background: #f6f8fa;
        border: 1.5px dashed #b3c6e0;
        border-radius: 5px;
        padding: 8px 12px;
        margin-bottom: 8px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 8px;
        transition:
          border 0.2s,
          background 0.2s;
        border: 1px dashed #9ca3af;
        &.drop-hover {
          border-color: #2868e7;
          background: #eaf2ff;
        }
        &.drop-disabled {
          border-color: #ff4d4f;
          background: #fff2f0;
        }
        &.disabled {
          background: #f5f5f5;
          border-color: #d9d9d9;
          cursor: not-allowed;
        }
        .selected-field {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          background: #eaf2ff;
          border-radius: 4px;
          padding: 4px 10px 4px 6px;
          font-size: 14px;
          color: #2868e7;

          .field-content {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            flex: 1;
            min-width: 0;
            gap: 8px;

            .field-left {
              display: flex;
              align-items: center;
              flex: 1;
              min-width: 0;

              .field-icon {
                margin-right: 4px;
                font-weight: bold;
              }

              .field-name {
                flex: 1;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                min-width: 0; // 确保 flex 子元素可以收缩
              }
            }

            .stat-method-select {
              flex-shrink: 0;
            }
          }

          .remove-btn {
            color: #999;
            font-size: 15px;
            cursor: pointer;
            flex-shrink: 0;
            margin-left: 8px;
            &:hover {
              color: #e74c3c;
            }
          }
        }
      }
      .chart-type-list {
        flex: 1;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
        margin-top: 8px;
        padding-right: 4px;
        overflow-y: auto;
        min-height: 180px;
        .chart-type-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          border-radius: 6px;
          border: 1px solid #f0f0f0;
          height: 100px;
          padding: 8px;

          cursor: pointer;
          transition:
            border 0.2s,
            background 0.2s;
          .chart-image {
            width: 100%;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            margin-bottom: 8px;
            background: #fafafa;
            border-radius: 6px;

            img {
              width: 80%;
              height: 80%;
            }
          }
          .chart-name {
            margin-bottom: 4px;
            font-size: 14px;
            color: #262628;
          }
          &:hover,
          &.selected {
            border: 1px solid #2868e7;
          }
        }
      }
    }
  }
  .add-chart-preview {
    flex: 1;
    background: #f5f8fe url('/assets/jmpdmvqh.svg') repeat;
    display: flex;
    flex-direction: column;
    padding: 16px;
    .preview-header {
      margin-bottom: 18px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .card-name-input {
        font-size: 18px;
        font-weight: 600;
        color: #222;
        background: transparent;
        border: 1px solid transparent;
        outline: none;
        box-shadow: none;
        padding: 4px 8px;
        margin-bottom: 0;
        border-radius: 4px;
        height: 38px;
        line-height: 1.5;

        &.editing {
          background: #fff;
          border: 1px solid #2868e7;
          box-shadow: 0 0 0 2px rgba(40, 104, 231, 0.2);
        }
      }

      .card-name-display {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        border: 1px solid transparent;
        height: 38px;
        transition: background-color 0.2s;

        &:hover {
          background: rgba(255, 255, 255, 0.8);
        }

        .card-name-text {
          font-size: 18px;
          font-weight: 600;
          color: #222;
          min-width: 120px;
          line-height: 1.5;
        }
      }
    }
    .preview-chart-area {
      flex: 1 1 0;
      display: flex;
      align-items: center;
      justify-content: center;
      .chart-preview-container {
        width: 100%;
        height: 100%;
        background: #fff;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        padding: 20px;

        .no-chart-selected {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 100%;

          .placeholder-text {
            font-size: 16px;
            color: #999;
            text-align: center;
          }
        }
      }
    }
  }
}
.chart-selector-modal {
  .ant-radio-button-wrapper {
    width: 50%;
    text-align: center;
  }
}
.sidebar-section {
  &:first-child {
    padding-bottom: 16px;
    border-bottom: 1px dashed #9ca3af;
  }
  &:last-child {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* 关键：允许 flex 子项收缩 */
  }
  .sidebar-title {
    height: 40px;
    line-height: 40px;
    font-size: 16px;
    font-weight: 600;
    color: #262628;
    margin-bottom: 10px;
  }
  .sidebar-link {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #2868e7;
    margin-top: 4px;
    cursor: pointer;
  }
}
