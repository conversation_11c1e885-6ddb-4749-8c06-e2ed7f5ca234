'use client';

import type React from 'react';
import { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import type { LineChartProps } from './types';
import type { ThemeConfigValues } from '@/components/ChartSettings';
import ChartContainer from './ChartContainer';
import { getThemeColors } from '@/config/theme';

interface ExtendedLineChartProps extends LineChartProps {
  themeConfig?: ThemeConfigValues;
}

const LineChart: React.FC<ExtendedLineChartProps> = ({ title, showData = true, smooth = true, themeConfig }) => {
  const chartRef = useRef<HTMLDivElement>(null);

  // 图表数据
  const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
  const chartData = [
    { month: '1月', value: 3.5 },
    { month: '2月', value: 4.2 },
    { month: '3月', value: 3.8 },
    { month: '4月', value: 5.1 },
    { month: '5月', value: 4.9 },
    { month: '6月', value: 6.2 },
    { month: '7月', value: 5.8 },
    { month: '8月', value: 6.5 },
    { month: '9月', value: 7.2 },
    { month: '10月', value: 6.8 },
    { month: '11月', value: 7.5 },
    { month: '12月', value: 8.0 },
  ];

  // 表格显示用的列定义
  const tableColumns = [
    { title: '月份', dataIndex: 'month', key: 'month' },
    { title: '数值', dataIndex: 'value', key: 'value' },
  ];

  useEffect(() => {
    if (!chartRef.current) return;

    const chart = echarts.init(chartRef.current);

    const option = {
      color: getThemeColors(themeConfig?.theme),
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        top: '15%',
        containLabel: true,
        show: themeConfig?.showGrid,
        lineStyle: {
          type: themeConfig?.gridStyle === '实线' ? 'solid' : themeConfig?.gridStyle === '虚线' ? 'dashed' : 'dotted',
          width: Number(themeConfig?.gridWidth) || 1,
          color: themeConfig?.gridColor || '#f0f0f0',
        },
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: months,
        axisLine: {
          lineStyle: {
            color: '#f0f0f0',
          },
        },
        axisLabel: {
          show: themeConfig?.showAxis,
          color: themeConfig?.textColor || 'rgba(0, 0, 0, 0.45)',
          fontSize: Number(themeConfig?.textSize) || 12,
          rotate: Number(themeConfig?.axisAngle?.replace('°', '')) || 0,
        },
      },
      yAxis: {
        type: 'value',
        max: themeConfig?.maxValue === '自动' ? undefined : Number(themeConfig?.maxValue),
        min: themeConfig?.minValue === '自动' ? undefined : Number(themeConfig?.minValue),
        interval: themeConfig?.dataInterval === '自动' ? undefined : Number(themeConfig?.dataInterval),
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: themeConfig?.showAxis,
          color: themeConfig?.textColor || 'rgba(0, 0, 0, 0.45)',
          fontSize: Number(themeConfig?.textSize) || 12,
        },
        splitLine: {
          show: themeConfig?.showGrid,
          lineStyle: {
            type: themeConfig?.gridStyle === '实线' ? 'solid' : themeConfig?.gridStyle === '虚线' ? 'dashed' : 'dotted',
            width: Number(themeConfig?.gridWidth) || 1,
            color: themeConfig?.gridColor || '#f0f0f0',
          },
        },
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          let value = params[0].value;
          if (themeConfig?.showPrefixUnit) {
            value = `￥${value}`;
          }
          if (themeConfig?.showSuffixUnit) {
            value = `${value}%`;
          }
          return `${params[0].name}: ${value}`;
        },
        backgroundColor: 'rgba(255, 255, 255, 0.8)',
        textStyle: {
          color: themeConfig?.textColor || '#333',
        },
        extraCssText: 'box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);',
      },
      legend: {
        show: themeConfig?.showLegend,
      },
      series: [
        {
          name: '系列1',
          type: 'line',
          smooth: smooth,
          symbolSize: 6,
          data: chartData.map((item) => item.value),
          label: {
            show: themeConfig?.showDataLabel,
            formatter: (params: any) => {
              let value = params.value;
              if (themeConfig?.showPrefixUnit) {
                value = `￥${value}`;
              }
              if (themeConfig?.showSuffixUnit) {
                value = `${value}%`;
              }
              return value;
            },
            fontSize: Number(themeConfig?.textSize) || 12,
            color: themeConfig?.textColor || 'rgba(0, 0, 0, 0.65)',
          },
        },
      ],
      animation: themeConfig?.showAnimation,
    };

    chart.setOption(option);

    const handleResize = () => {
      chart.resize();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      chart.dispose();
      window.removeEventListener('resize', handleResize);
    };
  }, [smooth, themeConfig]);

  return <div ref={chartRef} style={{ width: '100%', height: '100%' }} />;
};

// 包装LineChart，使其具备显示数据表格的能力
const LineChartWrapper: React.FC<ExtendedLineChartProps> = (props) => {
  // 图表数据
  const chartData = [
    { month: '1月', value: 3.5 },
    { month: '2月', value: 4.2 },
    { month: '3月', value: 3.8 },
    { month: '4月', value: 5.1 },
    { month: '5月', value: 4.9 },
    { month: '6月', value: 6.2 },
    { month: '7月', value: 5.8 },
    { month: '8月', value: 6.5 },
    { month: '9月', value: 7.2 },
    { month: '10月', value: 6.8 },
    { month: '11月', value: 7.5 },
    { month: '12月', value: 8.0 },
  ];

  // 表格显示用的列定义
  const tableColumns = [
    { title: '月份', dataIndex: 'month', key: 'month' },
    { title: '数值', dataIndex: 'value', key: 'value' },
  ];

  return (
    <ChartContainer title="折线图" data={chartData} columns={tableColumns}>
      <LineChart {...props} />
    </ChartContainer>
  );
};

export default LineChartWrapper;
