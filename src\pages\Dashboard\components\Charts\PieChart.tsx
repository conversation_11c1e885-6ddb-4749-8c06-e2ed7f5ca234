'use client';

import type React from 'react';
import { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import type { PieChartProps } from './types';
import type { ThemeConfigValues } from '@/components/ChartSettings';
import ChartContainer from './ChartContainer';
import { getThemeColors } from '@/config/theme';

interface ExtendedPieChartProps extends PieChartProps {
  themeConfig?: ThemeConfigValues;
}

const PieChart: React.FC<ExtendedPieChartProps> = ({
  title = '饼图数据分布',
  showData = true,
  height = '100%',
  width = '100%',
  radius = '60%',
  donut = false,
  themeConfig,
  data = [
    { name: '分类1', value: 40 },
    { name: '分类2', value: 30 },
    { name: '分类3', value: 20 },
    { name: '分类4', value: 10 },
  ],
}) => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    const chart = echarts.init(chartRef.current);

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          let value = params.value;
          if (themeConfig?.showPrefixUnit) {
            value = `￥${value}`;
          }
          if (themeConfig?.showSuffixUnit) {
            value = `${value}%`;
          }
          return `${params.name}: ${value} (${params.percent}%)`;
        },
      },
      legend: {
        show: themeConfig?.showLegend,
        orient: 'vertical',
        right: 10,
        top: 'center',
        type: 'scroll',
        data: data.map((item) => item.name),
        textStyle: {
          color: themeConfig?.textColor || 'rgba(0, 0, 0, 0.65)',
          fontSize: Number(themeConfig?.textSize) || 12,
        },
      },
      color: getThemeColors(themeConfig?.theme),
      series: [
        {
          name: title,
          type: 'pie',
          center: ['40%', '50%'],
          radius: donut ? ['40%', '70%'] : radius,
          avoidLabelOverlap: true,
          itemStyle: {
            borderRadius: 4,
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: themeConfig?.showDataLabel,
            formatter: (params: any) => {
              let value = params.value;
              if (themeConfig?.showPrefixUnit) {
                value = `￥${value}`;
              }
              if (themeConfig?.showSuffixUnit) {
                value = `${value}%`;
              }
              return `${params.name}: ${value}`;
            },
            fontSize: Number(themeConfig?.textSize) || 12,
            color: themeConfig?.textColor || 'rgba(0, 0, 0, 0.65)',
          },
          emphasis: {
            label: {
              show: true,
              fontSize: Number(themeConfig?.textSize) || 16,
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: true,
          },
          data: data,
        },
      ],
      animation: themeConfig?.showAnimation,
    };

    chart.setOption(option);

    const handleResize = () => {
      chart.resize();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      chart.dispose();
      window.removeEventListener('resize', handleResize);
    };
  }, [data, radius, donut, title, themeConfig]);

  return <div ref={chartRef} style={{ width, height }} />;
};

// 包装PieChart，使其具备显示数据表格的能力
const PieChartWrapper: React.FC<ExtendedPieChartProps> = (props) => {
  // 图表数据
  const chartData = [
    { name: '分类1', value: 40 },
    { name: '分类2', value: 30 },
    { name: '分类3', value: 20 },
    { name: '分类4', value: 10 },
  ];

  // 表格显示用的列定义
  const tableColumns = [
    { title: '类别', dataIndex: 'name', key: 'name' },
    { title: '数值', dataIndex: 'value', key: 'value' },
    {
      title: '百分比',
      key: 'percent',
      render: (text: any, record: any) => {
        const total = chartData.reduce((sum, item) => sum + item.value, 0);
        return `${((record.value / total) * 100).toFixed(2)}%`;
      },
    },
  ];

  return (
    <ChartContainer title="饼图" data={chartData} columns={tableColumns}>
      <PieChart {...props} />
    </ChartContainer>
  );
};

export default PieChartWrapper;
