'use client';

import type React from 'react';
import { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import type { FunnelChartProps } from './types';
import type { ThemeConfigValues } from '@/components/ChartSettings';
import ChartContainer from './ChartContainer';
import { getThemeColors } from '@/config/theme';

interface ExtendedFunnelChartProps extends FunnelChartProps {
  themeConfig?: ThemeConfigValues;
}

const FunnelChart: React.FC<ExtendedFunnelChartProps> = ({
  title = '漏斗图',
  showData = true,
  height = '100%',
  width = '100%',
  themeConfig,
  data = [
    { name: '访问', value: 100 },
    { name: '注册', value: 80 },
    { name: '下载', value: 60 },
    { name: '购买', value: 40 },
    { name: '复购', value: 20 },
  ],
  sort = 'descending',
}) => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    const chart = echarts.init(chartRef.current);

    const option = {
      color: getThemeColors(themeConfig?.theme),
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          let value = params.value;
          if (themeConfig?.showPrefixUnit) {
            value = `￥${value}`;
          }
          if (themeConfig?.showSuffixUnit) {
            value = `${value}%`;
          }
          return `${params.name}: ${value}`;
        },
      },
      legend: {
        show: themeConfig?.showLegend,
        data: data.map((item) => item.name),
        textStyle: {
          color: themeConfig?.textColor || 'rgba(0, 0, 0, 0.65)',
          fontSize: Number(themeConfig?.textSize) || 12,
        },
      },
      series: [
        {
          name: title,
          type: 'funnel',
          left: '10%',
          top: 60,
          bottom: 60,
          width: '80%',
          min: 0,
          max: Math.max(...data.map((item) => item.value)),
          minSize: '0%',
          maxSize: '100%',
          sort: sort,
          gap: 2,
          label: {
            show: themeConfig?.showDataLabel,
            position: 'inside',
            formatter: (params: any) => {
              let value = params.value;
              if (themeConfig?.showPrefixUnit) {
                value = `￥${value}`;
              }
              if (themeConfig?.showSuffixUnit) {
                value = `${value}%`;
              }
              return `${params.name}\n${value}`;
            },
            fontSize: Number(themeConfig?.textSize) || 12,
            color: themeConfig?.textColor || 'rgba(0, 0, 0, 0.65)',
          },
          labelLine: {
            length: 10,
            lineStyle: {
              width: 1,
              type: themeConfig?.gridStyle === '实线' ? 'solid' : themeConfig?.gridStyle === '虚线' ? 'dashed' : 'dotted',
            },
          },
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 1,
          },
          emphasis: {
            label: {
              fontSize: Number(themeConfig?.textSize) || 16,
            },
          },
          data: data,
        },
      ],
      animation: themeConfig?.showAnimation,
    };

    chart.setOption(option);

    const handleResize = () => {
      chart.resize();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      chart.dispose();
      window.removeEventListener('resize', handleResize);
    };
  }, [data, sort, title, themeConfig]);

  return <div ref={chartRef} style={{ width, height }} />;
};

// 包装FunnelChart，使其具备显示数据表格的能力
const FunnelChartWrapper: React.FC<ExtendedFunnelChartProps> = (props) => {
  // 图表数据
  const chartData = [
    { name: '访问', value: 100 },
    { name: '注册', value: 80 },
    { name: '下载', value: 60 },
    { name: '购买', value: 40 },
    { name: '复购', value: 20 },
  ];

  // 表格显示用的列定义
  const tableColumns = [
    { title: '阶段', dataIndex: 'name', key: 'name' },
    { title: '数值', dataIndex: 'value', key: 'value' },
    {
      title: '转化率',
      key: 'rate',
      render: (text: any, record: any, index: number) => {
        if (index === 0) return '100%';
        const prevValue = chartData[index - 1].value;
        return `${((record.value / prevValue) * 100).toFixed(2)}%`;
      },
    },
  ];

  return (
    <ChartContainer title="漏斗图" data={chartData} columns={tableColumns}>
      <FunnelChart {...props} />
    </ChartContainer>
  );
};

export default FunnelChartWrapper;
