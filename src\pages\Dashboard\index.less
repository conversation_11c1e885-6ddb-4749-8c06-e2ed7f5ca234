.dashboard-wrapper {
  height: 100%;
}
// 主容器
.dashboard-container {
  height: 100%;
  display: flex;
  background: #f5f5f5 url('/assets/jmpdmvqh.svg') repeat;
  background-size: cover;
  padding-top: 12px;

  .sidebar {
    width: 259px;
    height: 100%;
    background-color: #fff;
    border-right: 1px solid #f0f0f0;
    padding: 16px;
    border-radius: 0px 16px 16px 0px;
    background: #fff;
    box-shadow: 1px 4px 6px 0px rgba(8, 31, 50, 0.08);
    transition: all 0.3s ease;

    &.collapsed {
      width: 60px;
      padding: 16px 8px;

      .sidebar-header {
        justify-content: center;
        .header-actions {
          display: none;
        }
        span {
          display: none;
        }
      }

      .search-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;

        .search-icon {
          display: none;
        }

        .collapsed-icons {
          display: flex;
          flex-direction: column;
          gap: 24px;
          align-items: center;

          .icon {
            width: 16px;
            height: 16px;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              opacity: 0.8;
            }
          }
        }
      }

      .menu {
        .menu-item {
          padding: 8px;
          justify-content: center;

          .menu-item-name {
            display: none;
          }

          .more-button {
            display: none;
          }
        }
      }
    }

    .sidebar-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .header-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        img {
          cursor: pointer;
          width: 20px;
          height: 20px;
          &:hover {
            opacity: 0.8;
          }
        }
      }

      span {
        font-size: 14px;
      }

      .collapse-icon {
        cursor: pointer;
        width: 16px;
        height: 16px;
        margin-left: 8px;

        &:hover {
          opacity: 0.8;
        }
      }
    }

    .search-box {
      position: relative;
      margin-bottom: 16px;
    }

    .menu {
      height: calc(100% - 100px);
      overflow-y: auto;

      .menu-item {
        position: relative;
        padding: 8px 12px;
        margin-bottom: 4px;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;
        display: flex;
        justify-content: space-between;
        align-items: center;

        &:hover {
          background-color: #f5f5f5;
          .more-button {
            opacity: 1;
          }
        }

        &.active {
          background-color: #e5edfc;
          color: #2868e7;
          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            right: 0;
            display: block;
            width: 3px;
            height: 100%;
            background-color: #2868e7;
          }
        }

        .menu-item-container {
          display: flex;
          align-items: center;
          gap: 10px;
          flex: 1;
          min-width: 0; // 确保flex子元素可以收缩
          .menu-item-name {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            min-width: 0; // 确保文本可以被截断
          }
        }

        .more-button {
          opacity: 0;
          transition: opacity 0.3s;
          padding: 4px;
          height: 24px;
          width: 24px;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            background-color: rgba(0, 0, 0, 0.04);
          }
        }
      }
    }
  }

  .main-content {
    flex: 1;
    padding: 0 24px;

    .action-buttons {
      margin-bottom: 24px;
      display: flex;

      .action-buttons-left {
        flex: 1;
        display: flex;
        gap: 12px;
      }
      .action-buttons-right {
        flex: 1;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 8px;

        .ai-assistant-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;
          border-radius: 4px;
          border: 1px solid #d9d9d9;
          background: #2868e7;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(40, 104, 231, 0.3);
          }

          img {
            width: 20px;
            height: 20px;
          }
        }

        .share-btn {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 6px 12px;
          height: 32px;
          border-radius: 4px;
          border: 1px solid #2868e7;
          background-color: #fff;
          color: #2868e7;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(40, 104, 231, 0.3);
          }

          img {
            width: 14px;
            height: 14px;
          }
        }
      }

      button {
        padding: 4px 16px;
        border-radius: 4px;
      }
    }
    .dashboard-grid-container {
      height: calc(100vh - 180px);
      overflow-y: auto;
      .dashboard-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: 24px;

        .chart-container {
          border-radius: 16px;
          background: #fff;
          box-shadow: 0px 0px 5px 0px rgba(8, 31, 50, 0.05);
          overflow: hidden;
          transition: all 0.3s ease;
          border: 2px solid transparent;
          cursor: pointer;

          &.selected {
            background: #e6f4ff;
            box-shadow:
              0 4px 12px rgba(24, 144, 255, 0.15),
              0 0 0 1px rgba(24, 144, 255, 0.1);
            border: 1px solid #91caff;
          }

          .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;

            .chart-title {
              font-size: 14px;
              font-weight: 600;
              color: rgba(0, 0, 0, 0.85);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .chart-actions {
              display: flex;
              align-items: center;
              gap: 8px;
              font-size: 12px;

              .action-link {
                color: #2868e7;
                cursor: pointer;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;

                &:hover {
                  color: #40a9ff;
                }
              }

              .setting-icon {
                color: rgba(0, 0, 0, 0.45);
                cursor: pointer;

                &:hover {
                  color: rgba(0, 0, 0, 0.65);
                }
              }
            }
          }
          .chart-content {
            padding: 16px;
            height: 300px;
            position: relative;

            &.table-view {
              padding: 0;
              overflow: auto;
            }

            .data-table {
              width: 100%;
              border-collapse: collapse;

              th,
              td {
                padding: 8px 12px;
                text-align: left;
                border-bottom: 1px solid #f0f0f0;
                font-size: 12px;
              }

              th {
                background-color: #fafafa;
                font-weight: 600;
                color: rgba(0, 0, 0, 0.85);
              }

              tr:hover td {
                background-color: #f5f5f5;
              }
            }
          }
        }
      }

      .dashboard-empty-charts {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        padding: 40px 20px;

        &-content {
          text-align: center;
          max-width: 800px;
          width: 100%;
        }

        &-title {
          font-size: 40px;
          color: #262628;
          margin-bottom: 10px;
        }

        &-description {
          font-size: 18px;
          color: #6b7280;
          line-height: 1.6;
          margin-bottom: 32px;
        }

        &-button-container {
          display: flex;
          justify-content: center;
          margin-bottom: 40px;
        }

        &-button {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 20px;
          border-radius: 4px;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.3s ease;
          border: none;
          outline: none;

          img {
            width: 16px;
            height: 16px;
          }

          &.primary {
            min-width: 120px;
            height: 40px;
            padding: 0px 16px;
            border-radius: 4px;
            border: 1px solid #dce8ff;
            background: linear-gradient(95deg, #556fff 5.21%, #b640ff 98.16%);
            box-shadow: 0px 0px 2px 0px #9ebeff;
            color: #fff;
            transition: all 0.3s ease;

            // 改变颜色
            &:hover {
              transform: translateY(-1px);
            }
          }
        }

        .chart-types-grid {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: 20px;
          max-width: 800px;
          margin: 0 auto;

          .chart-type-item {
            width: 210px;
            height: 165px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.06);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid #f0f0f0;
            overflow: hidden;

            &:hover {
              box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
              border-color: #d9d9d9;
              transform: translateY(-2px);
            }

            .chart-icon {
              width: 100%;
              height: 110px;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-bottom: 12px;
              background: #f5f5f5;
              border-radius: 8px 8px 0 0;

              img {
                width: 100px;
                height: 100px;
                object-fit: contain;
              }
            }

            .chart-name {
              font-size: 14px;
              color: #333;
              font-weight: 400;
              text-align: center;
            }
          }

          @media (max-width: 768px) {
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;

            .chart-type-item {
              width: 160px;
              height: 120px;
              padding: 16px 12px;

              .chart-icon {
                width: 100%;
                height: 60px;
                margin-bottom: 8px;

                img {
                  width: 45px;
                  height: 45px;
                }
              }

              .chart-name {
                font-size: 12px;
              }
            }
          }

          @media (max-width: 480px) {
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;

            .chart-type-item {
              width: 140px;
              height: 100px;
              padding: 12px 8px;

              .chart-icon {
                width: 100%;
                height: 50px;
                margin-bottom: 6px;

                img {
                  width: 35px;
                  height: 35px;
                }
              }

              .chart-name {
                font-size: 11px;
              }
            }
          }
        }
      }
    }
  }

  .right-panel-container {
    width: 320px;
    overflow-y: auto;
    background-color: #fff;
    border-radius: 16px 0px 0px 16px;
    background: #fff;
    box-shadow: 1px 4px 6px 0px rgba(8, 31, 50, 0.08);
    transition: all 0.3s ease;

    &.collapsed {
      width: 0;
      overflow: hidden;
    }
  }
}

.dashboard-empty {
  min-height: 100%;
  background: url('/assets/86vbojnk.svg') no-repeat;
  background-size: cover;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  &-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 600px;
    padding: 0 20px;
  }

  &-title {
    font-size: 28px;
    font-weight: 600;
    background: linear-gradient(90deg, #106bec 0.13%, #c86ced 95.96%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 10px;
  }

  &-description {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    margin-bottom: 24px;
  }

  &-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
  }

  &-button {
    padding: 8px 20px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    outline: none;

    &.primary {
      background-color: #2d6cff;
      color: white;

      &:hover {
        background-color: #1a5ae0;
      }
    }

    &.secondary {
      background-color: #fff;
      color: #262628;
      border: 1px solid #d9d9d9;

      &:hover {
        background-color: #f5f5f5;
      }
    }
  }
}

// 右侧面板
.right-panel {
  width: 200px;
  background-color: white;
  border-left: 1px solid #e8e8e8;
  padding: 2px 24px;

  .panel-header {
    font-size: 14px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    padding-bottom: 16px;
    margin-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
  }
}
